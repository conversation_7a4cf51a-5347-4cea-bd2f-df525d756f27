# 柴管家项目结构规则

## 📋 文档目标

本文档为柴管家项目团队提供统一的项目结构规范，确保所有开发人员遵循一致的文件组织和目录创建标准，维护项目架构的完整性和可维护性。

## 📚 目录索引

- [1. 总体架构原则](#1-总体架构原则)
- [2. 目录结构规范](#2-目录结构规范)
- [3. 文件命名约定](#3-文件命名约定)
- [4. 模块创建指南](#4-模块创建指南)
- [5. 代码组织原则](#5-代码组织原则)
- [6. 测试文件规范](#6-测试文件规范)
- [7. 配置文件管理](#7-配置文件管理)
- [8. 代码审查检查清单](#8-代码审查检查清单)
- [9. 常见违规情况](#9-常见违规情况)

## 1. 总体架构原则

### 1.1 架构风格
- **模块化单体架构**：所有功能模块在同一代码库中，但保持清晰的边界
- **分层架构**：严格遵循 API层 → 业务层 → 数据层 → 基础设施层
- **事件驱动**：模块间通过消息队列异步通信，避免直接依赖

### 1.2 核心设计原则
- **单一职责**：每个目录和文件只负责一个明确的功能
- **关注分离**：不同层次的代码严格分离，不允许跨层直接调用
- **统一标准**：所有模块必须遵循相同的目录结构和命名规范

## 2. 目录结构规范

### 2.1 根目录结构

```
chaiguanjia/
├── frontend/           # 前端应用（React + TypeScript）
├── backend/           # 后端应用（FastAPI + Python）
├── infrastructure/    # 基础设施配置
├── docs/             # 项目文档
├── tests/            # 端到端测试
├── .github/          # CI/CD配置
├── README.md         # 项目说明
├── .gitignore       # Git忽略文件
├── .env.example     # 环境变量示例
└── docker-compose.yml # 开发环境配置
```

**规则**：
- ✅ 根目录只能包含上述指定的目录和文件
- ❌ 禁止在根目录创建其他目录或文件
- ✅ 所有目录名使用小写字母和下划线

### 2.2 前端目录结构

```
frontend/
├── src/
│   ├── components/    # 通用组件
│   ├── pages/        # 页面组件
│   ├── hooks/        # 自定义Hooks
│   ├── services/     # API服务
│   ├── stores/       # 状态管理
│   ├── utils/        # 工具函数
│   ├── types/        # TypeScript类型定义
│   ├── assets/       # 静态资源
│   │   ├── images/   # 图片资源
│   │   ├── icons/    # 图标资源
│   │   └── styles/   # 样式文件
│   ├── layouts/      # 布局组件
│   ├── contexts/     # React Context
│   └── constants/    # 常量定义
├── public/           # 公共静态资源
├── package.json
├── tsconfig.json
└── tailwind.config.js
```

**规则**：
- ✅ 所有React组件文件使用PascalCase命名（如：`UserProfile.tsx`）
- ✅ 工具函数和服务文件使用camelCase命名（如：`apiClient.ts`）
- ✅ 类型定义文件使用PascalCase + Type后缀（如：`UserType.ts`）
- ❌ 禁止在src目录下创建其他顶级目录

### 2.3 后端目录结构

```
backend/
├── app/
│   ├── main.py              # FastAPI应用入口
│   ├── config/              # 配置管理
│   │   ├── environments/    # 环境配置
│   │   ├── settings.py      # 基础配置
│   │   ├── database.py      # 数据库配置
│   │   ├── cache.py         # 缓存配置
│   │   ├── messaging.py     # 消息队列配置
│   │   ├── security.py      # 安全配置
│   │   └── logging.py       # 日志配置
│   ├── core/                # 核心功能
│   ├── middleware/          # 中间件管理
│   ├── plugins/             # 插件扩展
│   ├── modules/             # 业务模块
│   ├── shared/              # 共享组件
│   └── api/                 # API路由聚合
├── database/                # 数据库结构设计
├── alembic/                 # 数据库迁移
├── tests/                   # 集成测试
├── requirements.txt         # Python依赖
├── Dockerfile
└── .env.example            # 环境变量示例
```

**规则**：
- ✅ 所有Python文件使用snake_case命名（如：`user_service.py`）
- ✅ 所有目录必须包含`__init__.py`文件
- ✅ 类名使用PascalCase（如：`UserService`）
- ❌ 禁止在app目录下创建其他顶级目录

## 3. 文件命名约定

### 3.1 基于GNC-AIDD命名规范

| 文件类型 | 命名规则 | 示例 |
|---------|---------|------|
| Python模块文件 | snake_case | `user_service.py` |
| Python类 | PascalCase | `class UserService` |
| Python函数/方法 | snake_case | `def get_user_by_id()` |
| Python常量 | UPPER_SNAKE_CASE | `MAX_RETRY_COUNT = 5` |
| TypeScript文件 | camelCase | `userService.ts` |
| React组件 | PascalCase | `UserProfile.tsx` |
| 配置文件 | snake_case | `database_config.py` |
| 测试文件 | test_前缀 + snake_case | `test_user_service.py` |

### 3.2 特殊文件命名规则

- **API路由文件**：`routers.py`（固定名称）
- **数据模型文件**：`models.py` 或具体模型名如 `user.py`
- **服务文件**：`{模块名}_service.py`
- **Schema文件**：`schemas.py`（固定名称）
- **依赖注入文件**：`dependencies.py`（固定名称）

## 4. 模块创建指南

### 4.1 业务模块标准结构

每个业务模块必须严格遵循以下三层架构：

```
modules/{module_name}/
├── __init__.py
├── api/                    # API层
│   ├── __init__.py
│   ├── routers.py         # 路由定义
│   ├── schemas.py         # 请求/响应模型
│   └── dependencies.py    # 依赖注入
├── services/              # 业务层
│   ├── __init__.py
│   └── {module_name}_service.py
├── models/                # 数据层
│   ├── __init__.py
│   └── {module_name}.py
└── tests/                 # 测试层
    ├── __init__.py
    ├── test_api.py
    ├── test_services.py
    └── features/          # BDD测试场景
```

### 4.2 创建新模块步骤

1. **创建目录结构**：
```bash
mkdir -p backend/app/modules/{new_module_name}/{api,services,models,tests/features}
```

2. **创建必需文件**：
```bash
touch backend/app/modules/{new_module_name}/__init__.py
touch backend/app/modules/{new_module_name}/api/{__init__.py,routers.py,schemas.py,dependencies.py}
touch backend/app/modules/{new_module_name}/services/{__init__.py,{new_module_name}_service.py}
touch backend/app/modules/{new_module_name}/models/{__init__.py,{new_module_name}.py}
touch backend/app/modules/{new_module_name}/tests/{__init__.py,test_api.py,test_services.py}
```

3. **验证结构**：确保新模块结构与现有模块完全一致

### 4.3 模块命名规则

- ✅ 使用snake_case命名（如：`user_management`）
- ✅ 名称应清晰描述模块功能
- ✅ 避免使用缩写，使用完整单词
- ❌ 禁止使用驼峰命名或其他格式

## 5. 代码组织原则

### 5.1 API层组织原则

**routers.py**：
```python
from fastapi import APIRouter, Depends
from .schemas import UserCreateSchema, UserResponseSchema
from .dependencies import get_current_user
from ..services.user_service import UserService

router = APIRouter(prefix="/users", tags=["users"])

@router.post("/", response_model=UserResponseSchema)
async def create_user(user_data: UserCreateSchema):
    # 路由处理逻辑
    pass
```

**schemas.py**：
```python
from pydantic import BaseModel
from typing import Optional

class UserCreateSchema(BaseModel):
    username: str
    email: str
    
class UserResponseSchema(BaseModel):
    id: int
    username: str
    email: str
```

**dependencies.py**：
```python
from fastapi import Depends, HTTPException
from ..shared.security.jwt_handler import verify_token

async def get_current_user(token: str = Depends(verify_token)):
    # 依赖注入逻辑
    pass
```

### 5.2 业务层组织原则

**{module_name}_service.py**：
```python
from typing import List, Optional
from ..models.user import User
from ..shared.database.session import get_db_session

class UserService:
    def __init__(self):
        self.db = get_db_session()
    
    async def create_user(self, user_data: dict) -> User:
        # 业务逻辑实现
        pass
    
    async def get_user_by_id(self, user_id: int) -> Optional[User]:
        # 业务逻辑实现
        pass
```

### 5.3 数据层组织原则

**{module_name}.py**：
```python
from sqlalchemy import Column, Integer, String, DateTime
from ..shared.database.base import Base

class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True)
    email = Column(String(100), unique=True, index=True)
    created_at = Column(DateTime, default=datetime.utcnow)

## 6. 测试文件规范

### 6.1 测试目录结构

```
tests/
├── __init__.py
├── test_api.py           # API层测试
├── test_services.py      # 业务层测试
└── features/            # BDD测试场景
    ├── user_management.feature
    └── step_definitions/
```

### 6.2 测试文件命名规则

- **单元测试**：`test_{被测试文件名}.py`
- **集成测试**：`test_integration_{功能名}.py`
- **BDD测试**：`{功能名}.feature`
- **测试配置**：`conftest.py`

### 6.3 测试文件模板

**test_api.py**：
```python
import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

class TestUserAPI:
    def test_create_user_success(self):
        response = client.post("/users/", json={
            "username": "testuser",
            "email": "<EMAIL>"
        })
        assert response.status_code == 201

    def test_create_user_invalid_data(self):
        response = client.post("/users/", json={})
        assert response.status_code == 422
```

**test_services.py**：
```python
import pytest
from unittest.mock import Mock, patch
from app.modules.user_management.services.user_service import UserService

class TestUserService:
    @pytest.fixture
    def user_service(self):
        return UserService()

    async def test_create_user_success(self, user_service):
        user_data = {"username": "testuser", "email": "<EMAIL>"}
        result = await user_service.create_user(user_data)
        assert result.username == "testuser"
```

### 6.4 BDD测试规范

**user_management.feature**：
```gherkin
Feature: 用户管理
  作为系统管理员
  我希望能够管理用户账户
  以便控制系统访问权限

  Scenario: 创建新用户
    Given 我是系统管理员
    When 我创建一个新用户 "testuser"
    Then 用户应该被成功创建
    And 用户状态应该是 "active"
```

## 7. 配置文件管理

### 7.1 环境配置结构

```
backend/app/config/
├── __init__.py
├── environments/
│   ├── __init__.py
│   ├── development.py    # 开发环境
│   ├── testing.py       # 测试环境
│   ├── staging.py       # 预发布环境
│   └── production.py    # 生产环境
├── settings.py          # 基础配置
├── database.py          # 数据库配置
├── cache.py            # 缓存配置
├── messaging.py        # 消息队列配置
├── security.py         # 安全配置
└── logging.py          # 日志配置
```

### 7.2 配置文件命名规则

- **环境配置**：`{environment}.py`（如：`development.py`）
- **功能配置**：`{功能名}.py`（如：`database.py`）
- **基础配置**：`settings.py`（固定名称）

### 7.3 配置文件模板

**environments/development.py**：
```python
from .base import BaseConfig

class DevelopmentConfig(BaseConfig):
    DEBUG = True
    DATABASE_URL = "postgresql://localhost:5432/chaiguanjia_dev"
    REDIS_URL = "redis://localhost:6379/0"
    LOG_LEVEL = "DEBUG"
```

**database.py**：
```python
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from .settings import get_settings

settings = get_settings()

engine = create_engine(settings.DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()
```

## 8. 代码审查检查清单

### 8.1 目录结构检查

- [ ] 新增目录是否符合项目结构规范
- [ ] 目录命名是否使用snake_case
- [ ] 是否包含必需的`__init__.py`文件
- [ ] 业务模块是否遵循三层架构

### 8.2 文件命名检查

- [ ] Python文件是否使用snake_case命名
- [ ] 类名是否使用PascalCase
- [ ] 函数名是否使用snake_case
- [ ] 常量是否使用UPPER_SNAKE_CASE
- [ ] 测试文件是否以test_开头

### 8.3 代码组织检查

- [ ] API层是否只包含路由定义和请求处理
- [ ] 业务层是否包含核心业务逻辑
- [ ] 数据层是否只包含数据模型定义
- [ ] 是否存在跨层直接调用

### 8.4 测试覆盖检查

- [ ] 是否为新功能编写了单元测试
- [ ] 是否为API端点编写了集成测试
- [ ] 复杂业务逻辑是否有BDD测试覆盖
- [ ] 测试文件是否放在正确位置

## 9. 常见违规情况

### 9.1 目录结构违规

❌ **错误示例**：
```
backend/app/modules/user/
├── user_api.py          # 违规：应该在api/目录下
├── user_service.py      # 违规：应该在services/目录下
└── user_model.py        # 违规：应该在models/目录下
```

✅ **正确示例**：
```
backend/app/modules/user_management/
├── api/
│   ├── routers.py
│   ├── schemas.py
│   └── dependencies.py
├── services/
│   └── user_service.py
├── models/
│   └── user.py
└── tests/
```

### 9.2 命名违规

❌ **错误示例**：
```python
# 违规：使用驼峰命名
class userService:
    def getUserById(self, userId):
        pass

# 违规：使用缩写
def get_usr_by_id(usr_id):
    pass
```

✅ **正确示例**：
```python
# 正确：使用规范命名
class UserService:
    def get_user_by_id(self, user_id):
        pass
```

### 9.3 跨层调用违规

❌ **错误示例**：
```python
# 在API层直接调用数据层
from ..models.user import User

@router.get("/users/{user_id}")
async def get_user(user_id: int):
    # 违规：API层直接操作数据模型
    user = User.query.filter_by(id=user_id).first()
    return user
```

✅ **正确示例**：
```python
# 通过业务层调用
from ..services.user_service import UserService

@router.get("/users/{user_id}")
async def get_user(user_id: int):
    # 正确：通过业务层处理
    user_service = UserService()
    user = await user_service.get_user_by_id(user_id)
    return user
```

### 9.4 纠正方法

1. **立即纠正**：发现违规立即修改，不允许合并到主分支
2. **重构计划**：对于大范围违规，制定重构计划逐步修正
3. **团队培训**：定期进行规范培训，确保团队理解规则
4. **自动化检查**：配置CI/CD流水线自动检查规范遵循情况

## 10. 执行和监督

### 10.1 强制执行措施

- **代码审查**：所有代码必须通过结构规范检查才能合并
- **自动化检查**：CI/CD流水线自动验证目录结构和命名规范
- **定期审计**：每月进行项目结构审计，发现并纠正违规

### 10.2 例外处理

- **临时例外**：紧急情况下可申请临时例外，但必须在下个迭代中修正
- **规范更新**：规范需要更新时，必须经过团队讨论和文档更新

---

**文档版本**：v1.0
**最后更新**：2024-08-05
**维护者**：柴管家开发团队

> 本规则文档具有强制性，所有团队成员必须严格遵守。违反规范的代码将不被接受合并到主分支。
